import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../models/tweet.dart';

class ApiService {
  static const String baseUrl =
      'http://localhost/x22/x22_twitter_clone/backend/api';

  // Auth endpoints
  static const String loginEndpoint = '$baseUrl/auth/login.php';
  static const String registerEndpoint = '$baseUrl/auth/register.php';

  // Tweet endpoints
  static const String tweetsEndpoint = '$baseUrl/tweets/read.php';
  static const String createTweetEndpoint = '$baseUrl/tweets/create.php';
  static const String likeTweetEndpoint = '$baseUrl/tweets/like.php';
  static const String unlikeTweetEndpoint = '$baseUrl/tweets/unlike.php';

  // Get stored token
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Store token
  static Future<void> storeToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  // Remove token
  static Future<void> removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  // Get headers with auth token
  static Future<Map<String, String>> getHeaders() async {
    final token = await getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Login
  static Future<Map<String, dynamic>> login(
    String email,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(loginEndpoint),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email, 'password': password}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        await storeToken(data['token']);
        return {
          'success': true,
          'user': User.fromJson(data['user']),
          'token': data['token'],
        };
      } else {
        return {'success': false, 'message': data['message'] ?? 'Login failed'};
      }
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  // Register
  static Future<Map<String, dynamic>> register({
    required String username,
    required String email,
    required String password,
    required String displayName,
    String? bio,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(registerEndpoint),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': username,
          'email': email,
          'password': password,
          'display_name': displayName,
          'bio': bio ?? '',
        }),
      );

      final data = jsonDecode(response.body);

      return {
        'success': response.statusCode == 201,
        'message': data['message'],
      };
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  // Get tweets
  static Future<List<Tweet>> getTweets({
    int limit = 20,
    int offset = 0,
    int? userId,
  }) async {
    try {
      String url = '$tweetsEndpoint?limit=$limit&offset=$offset';
      if (userId != null) {
        url += '&user_id=$userId';
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> tweetsJson = data['records'];
        return tweetsJson.map((json) => Tweet.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load tweets');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Create tweet
  static Future<Map<String, dynamic>> createTweet({
    required String content,
    String? imageUrl,
    int? replyToTweetId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(createTweetEndpoint),
        headers: await getHeaders(),
        body: jsonEncode({
          'content': content,
          'image_url': imageUrl,
          'reply_to_tweet_id': replyToTweetId,
        }),
      );

      final data = jsonDecode(response.body);

      return {
        'success': response.statusCode == 201,
        'message': data['message'],
        'tweet_id': data['tweet_id'],
      };
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  // Like tweet
  static Future<Map<String, dynamic>> likeTweet(int tweetId) async {
    try {
      final response = await http.post(
        Uri.parse(likeTweetEndpoint),
        headers: await getHeaders(),
        body: jsonEncode({'tweet_id': tweetId}),
      );

      final data = jsonDecode(response.body);

      return {
        'success': response.statusCode == 200,
        'message': data['message'],
      };
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  // Unlike tweet
  static Future<Map<String, dynamic>> unlikeTweet(int tweetId) async {
    try {
      final response = await http.post(
        Uri.parse(unlikeTweetEndpoint),
        headers: await getHeaders(),
        body: jsonEncode({'tweet_id': tweetId}),
      );

      final data = jsonDecode(response.body);

      return {
        'success': response.statusCode == 200,
        'message': data['message'],
      };
    } catch (e) {
      return {'success': false, 'message': 'Network error: $e'};
    }
  }

  // Logout
  static Future<void> logout() async {
    await removeToken();
  }
}
